File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,16.247496%,8269,96,7718,551,0,96
sdio_sdcard.o,14.088104%,7170,68,7134,0,36,32
ff.o,10.115141%,5148,6,5142,0,6,0
oled.o,7.812316%,3976,22,1242,2712,22,0
ebtn.o,4.067277%,2070,60,2070,0,0,60
btod.o,3.803985%,1936,0,1936,0,0,0
mcu_cmic_gd32f470vet6.o,3.261681%,1660,592,1640,0,20,572
sd_app.o,3.124140%,1590,1452,1574,0,16,1436
gd32f4xx_dma.o,2.617204%,1332,0,1332,0,0,0
gd25qxx.o,2.349982%,1196,0,1196,0,0,0
_printf_fp_dec.o,2.070971%,1054,0,1054,0,0,0
gd32f4xx_rcu.o,1.697646%,864,0,864,0,0,0
perf_counter.o,1.666208%,848,64,780,4,64,0
_printf_fp_hex.o,1.575824%,802,0,764,38,0,0
system_gd32f4xx.o,1.371478%,698,4,694,0,4,0
gd32f4xx_adc.o,1.285024%,654,0,654,0,0,0
gd32f4xx_usart.o,1.265375%,644,0,644,0,0,0
gd32f4xx_sdio.o,1.233937%,628,0,628,0,0,0
gd32f4xx_timer.o,1.155342%,588,0,588,0,0,0
btn_app.o,1.108186%,564,196,354,14,196,0
gd32f4xx_i2c.o,0.974575%,496,0,496,0,0,0
startup_gd32f450_470.o,0.966715%,492,2048,64,428,0,2048
gd32f4xx_rtc.o,0.950996%,484,0,484,0,0,0
__printf_flags_ss_wp.o,0.803631%,409,0,392,17,0,0
bigflt0.o,0.738790%,376,0,228,148,0,0
lc_ctype_c.o,0.620898%,316,0,44,272,0,0
diskio.o,0.620898%,316,0,316,0,0,0
gd32f4xx_gpio.o,0.514795%,262,0,262,0,0,0
fz_wm.l,0.503006%,256,0,256,0,0,0
oled_app.o,0.503006%,256,0,256,0,0,0
led_app.o,0.493182%,251,7,244,0,7,0
lludivv7m.o,0.467639%,238,0,238,0,0,0
gd32f4xx_misc.o,0.424412%,216,0,216,0,0,0
gd32f4xx_dac.o,0.389044%,198,0,198,0,0,0
_printf_wctomb.o,0.385114%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.369395%,188,0,148,40,0,0
_printf_intcommon.o,0.349747%,178,0,178,0,0,0
scheduler.o,0.345817%,176,76,100,0,76,0
systick.o,0.330098%,168,4,164,0,4,0
gd32f4xx_it.o,0.330098%,168,0,168,0,0,0
usart_app.o,0.312414%,159,515,156,0,3,512
perfc_port_default.o,0.302590%,154,0,154,0,0,0
fnaninf.o,0.275082%,140,0,140,0,0,0
rt_memcpy_v6.o,0.271152%,138,0,138,0,0,0
lludiv10.o,0.271152%,138,0,138,0,0,0
strcmpv7m.o,0.251503%,128,0,128,0,0,0
_printf_fp_infnan.o,0.251503%,128,0,128,0,0,0
_printf_longlong_dec.o,0.243644%,124,0,124,0,0,0
_printf_dec.o,0.235784%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.220065%,112,0,112,0,0,0
gd32f4xx_spi.o,0.204346%,104,0,104,0,0,0
rt_memcpy_w.o,0.196487%,100,0,100,0,0,0
__dczerorl2.o,0.176838%,90,0,90,0,0,0
memcmp.o,0.172908%,88,0,88,0,0,0
f2d.o,0.168979%,86,0,86,0,0,0
main.o,0.168979%,86,0,86,0,0,0
_printf_str.o,0.161119%,82,0,82,0,0,0
rt_memclr_w.o,0.153260%,78,0,78,0,0,0
_printf_pad.o,0.153260%,78,0,78,0,0,0
sys_stackheap_outer.o,0.145400%,74,0,74,0,0,0
llsdiv.o,0.141471%,72,0,72,0,0,0
lc_numeric_c.o,0.141471%,72,0,44,28,0,0
rt_memclr.o,0.133611%,68,0,68,0,0,0
_wcrtomb.o,0.125752%,64,0,64,0,0,0
strlen.o,0.121822%,62,0,62,0,0,0
rtc_app.o,0.117892%,60,0,60,0,0,0
vsnprintf.o,0.102173%,52,0,52,0,0,0
__scatter.o,0.102173%,52,0,52,0,0,0
m_wm.l,0.094314%,48,0,48,0,0,0
fpclassify.o,0.094314%,48,0,48,0,0,0
_printf_char_common.o,0.094314%,48,0,48,0,0,0
_printf_wchar.o,0.086454%,44,0,44,0,0,0
_printf_char.o,0.086454%,44,0,44,0,0,0
__2sprintf.o,0.086454%,44,0,44,0,0,0
_printf_charcount.o,0.078595%,40,0,40,0,0,0
libinit2.o,0.074665%,38,0,38,0,0,0
init_aeabi.o,0.070735%,36,0,36,0,0,0
_printf_truncate.o,0.070735%,36,0,36,0,0,0
systick_wrapper_ual.o,0.062876%,32,0,32,0,0,0
__scatter_zi.o,0.055016%,28,0,28,0,0,0
gd32f4xx_pmu.o,0.039297%,20,0,20,0,0,0
adc_app.o,0.039297%,20,0,20,0,0,0
exit.o,0.035368%,18,0,18,0,0,0
rt_ctype_table.o,0.031438%,16,0,16,0,0,0
_snputc.o,0.031438%,16,0,16,0,0,0
__printf_wp.o,0.027508%,14,0,14,0,0,0
dretinf.o,0.023578%,12,0,12,0,0,0
sys_exit.o,0.023578%,12,0,12,0,0,0
__rtentry2.o,0.023578%,12,0,12,0,0,0
fpinit.o,0.019649%,10,0,10,0,0,0
rtexit2.o,0.019649%,10,0,10,0,0,0
_sputc.o,0.019649%,10,0,10,0,0,0
_printf_ll.o,0.019649%,10,0,10,0,0,0
_printf_l.o,0.019649%,10,0,10,0,0,0
rt_locale_intlibspace.o,0.015719%,8,0,8,0,0,0
libspace.o,0.015719%,8,96,8,0,0,96
__main.o,0.015719%,8,0,8,0,0,0
heapauxi.o,0.011789%,6,0,6,0,0,0
_printf_x.o,0.011789%,6,0,6,0,0,0
_printf_u.o,0.011789%,6,0,6,0,0,0
_printf_s.o,0.011789%,6,0,6,0,0,0
_printf_p.o,0.011789%,6,0,6,0,0,0
_printf_o.o,0.011789%,6,0,6,0,0,0
_printf_n.o,0.011789%,6,0,6,0,0,0
_printf_ls.o,0.011789%,6,0,6,0,0,0
_printf_llx.o,0.011789%,6,0,6,0,0,0
_printf_llu.o,0.011789%,6,0,6,0,0,0
_printf_llo.o,0.011789%,6,0,6,0,0,0
_printf_lli.o,0.011789%,6,0,6,0,0,0
_printf_lld.o,0.011789%,6,0,6,0,0,0
_printf_lc.o,0.011789%,6,0,6,0,0,0
_printf_i.o,0.011789%,6,0,6,0,0,0
_printf_g.o,0.011789%,6,0,6,0,0,0
_printf_f.o,0.011789%,6,0,6,0,0,0
_printf_e.o,0.011789%,6,0,6,0,0,0
_printf_d.o,0.011789%,6,0,6,0,0,0
_printf_c.o,0.011789%,6,0,6,0,0,0
_printf_a.o,0.011789%,6,0,6,0,0,0
__rtentry4.o,0.011789%,6,0,6,0,0,0
printf2.o,0.007859%,4,0,4,0,0,0
printf1.o,0.007859%,4,0,4,0,0,0
_printf_percent_end.o,0.007859%,4,0,4,0,0,0
use_no_semi.o,0.003930%,2,0,2,0,0,0
rtexit.o,0.003930%,2,0,2,0,0,0
libshutdown2.o,0.003930%,2,0,2,0,0,0
libshutdown.o,0.003930%,2,0,2,0,0,0
libinit.o,0.003930%,2,0,2,0,0,0
