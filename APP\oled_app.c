/* Licence
* Company: MCUSTUDIO
* Auther: Ahypnis.
* Version: V0.10
* Time: 2025/06/05
* Note:
*/

#include "mcu_cmic_gd32f470vet6.h"

extern uint16_t adc_value[1];

// 静态缓冲区，避免栈溢出
static char oled_printf_buffer[128];

/**
 * @brief	使用类似printf的方式显示字符串，显示6x8像素的ASCII字符
 * @param x  Character position on the X-axis  range：0 - 127
 * @param y  Character position on the Y-axis  range：0 - 3
 * 例如：oled_printf(0, 0, "Data = %d", dat);
 **/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  va_list arg;      // 可变参数列表
  int len;          // 返回字符串长度

  va_start(arg, format);
  // 安全格式化字符串到 buffer
  len = vsnprintf(oled_printf_buffer, sizeof(oled_printf_buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, oled_printf_buffer, 8);
  return len;
}

void oled_task(void)
{
    oled_printf(0, 0, "KEY STA: %d%d%d%d%d%d", KEY1_READ, KEY2_READ, KEY3_READ, KEY4_READ, KEY5_READ, KEY6_READ);
    oled_printf(0, 1, "uwTick:%ld", get_system_ms());
    oled_printf(0, 2, "ADC: %d  ", adc_value[0]);
}

/* CUSTOM EDIT */
